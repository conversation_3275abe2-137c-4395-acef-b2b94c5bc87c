#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的时序数据模型
只存储原始硬件数据，在代码层面进行解析
基于硬件信息查询的原始数据格式
"""

import os
from datetime import datetime
from sqlalchemy import Index, text, ARRAY, SmallInteger, Integer, Float, DateTime
from sqlalchemy.dialects.postgresql import REAL
from models.database import db


class TimeSeriesData(db.Model):
    """简化的时序数据模型，只存储原始硬件数据"""

    # 根据环境设置表名
    if os.environ.get('FLASK_ENV') == 'development':
        __tablename__ = 'dev_time_series_data'
    else:
        __tablename__ = 'prod_time_series_data'

    # 主键
    id = db.Column(db.BigInteger, primary_key=True)

    # 基础字段
    device_id = db.Column(db.String(50), nullable=False, index=True)  # 设备ID
    timestamp = db.Column(DateTime(timezone=True), nullable=False, index=True)  # 时间戳

    # 原始硬件数据字段 - 基于HandleHardwareInfoQuery函数的数据结构
    # BL0910错误计数
    bl0910_error_count = db.Column(Integer, nullable=True)

    # 10个通道的BL0910 RMS寄存器值
    bl0910_rms_values = db.Column(ARRAY(Integer, dimensions=1), nullable=True)  # 10个uint32值

    # 继电器状态 (16位) - 使用Integer以支持无符号16位值
    relay_state = db.Column(Integer, nullable=True)

    # 周期错误计数
    short_period_error_count = db.Column(SmallInteger, nullable=True)
    long_period_error_count = db.Column(SmallInteger, nullable=True)

    # 最后零交叉时间
    last_zero_cross_time = db.Column(Integer, nullable=True)

    # 电压 (原始值 * 100)
    voltage_raw = db.Column(SmallInteger, nullable=True)

    # 温度 (原始值 * 100)
    temperature_raw = db.Column(SmallInteger, nullable=True)

    # 总有功功率 (原始值 * 100)
    total_power_raw = db.Column(Integer, nullable=True)

    # 信号质量
    csq = db.Column(SmallInteger, nullable=True)  # 信号质量
    ber = db.Column(SmallInteger, nullable=True)  # 误码率

    # 继电器故障状态
    relay_pull_fault = db.Column(SmallInteger, nullable=True)  # 继电器拉合故障
    relay_open_fault = db.Column(SmallInteger, nullable=True)  # 继电器分断故障

    # 元数据
    created_at = db.Column(DateTime(timezone=True), default=datetime.now, nullable=False)

    # 优化的索引策略
    __table_args__ = (
        # 主要查询索引
        Index('idx_device_timestamp', 'device_id', 'timestamp'),
        Index('idx_timestamp_device', 'timestamp', 'device_id'),

        # 按日期分区的索引
        Index('idx_date_device', text("date_trunc('day', timestamp)"), 'device_id'),
        Index('idx_hour_device', text("date_trunc('hour', timestamp)"), 'device_id'),

        # 数值范围查询索引（只对非空值建索引）
        Index('idx_voltage_range', 'voltage_raw', postgresql_where=text('voltage_raw IS NOT NULL')),
        Index('idx_temperature_range', 'temperature_raw', postgresql_where=text('temperature_raw IS NOT NULL')),
        Index('idx_total_power_range', 'total_power_raw', postgresql_where=text('total_power_raw IS NOT NULL')),

        # 错误监控索引
        Index('idx_bl0910_error', 'bl0910_error_count', postgresql_where=text('bl0910_error_count > 0')),
        Index('idx_period_errors', 'short_period_error_count', 'long_period_error_count',
              postgresql_where=text('short_period_error_count > 0 OR long_period_error_count > 0')),

        # 继电器状态索引
        Index('idx_relay_faults', 'relay_pull_fault', 'relay_open_fault',
              postgresql_where=text('relay_pull_fault > 0 OR relay_open_fault > 0')),
    )

    # 解析方法 - 在代码层面进行数据解析
    def get_voltage(self) -> float:
        """获取电压值（伏特）"""
        if self.voltage_raw is not None:
            return self.voltage_raw / 100.0
        return None

    def get_temperature(self) -> float:
        """获取温度值（摄氏度）"""
        if self.temperature_raw is not None:
            return self.temperature_raw / 100.0
        return None

    def get_total_power(self) -> float:
        """获取总功率值（瓦特）"""
        if self.total_power_raw is not None:
            return self.total_power_raw / 100.0
        return None

    def get_relay_states(self) -> dict:
        """解析继电器状态位"""
        if self.relay_state is None:
            return {}

        states = {}
        for i in range(16):  # 假设最多16个继电器
            states[f'relay_{i+1}'] = bool(self.relay_state & (1 << i))
        return states

    def get_channel_power(self, channel: int) -> float:
        """
        根据BL0910 RMS寄存器值计算指定通道的功率
        这里需要根据实际的BL0910计算公式进行转换
        """
        if (self.bl0910_rms_values is None or
            channel < 1 or channel > 10 or
            len(self.bl0910_rms_values) < channel):
            return None

        # 这里需要根据BL0910的实际计算公式进行转换
        # 暂时返回原始值，实际使用时需要根据硬件规格进行计算
        rms_value = self.bl0910_rms_values[channel - 1]
        # TODO: 实现实际的功率计算公式
        return float(rms_value) if rms_value is not None else None

    def get_all_channel_powers(self) -> list:
        """获取所有通道的功率值"""
        if self.bl0910_rms_values is None:
            return [None] * 10

        powers = []
        for i in range(10):
            if i < len(self.bl0910_rms_values):
                # TODO: 实现实际的功率计算公式
                rms_value = self.bl0910_rms_values[i]
                powers.append(float(rms_value) if rms_value is not None else None)
            else:
                powers.append(None)
        return powers

    def has_errors(self) -> bool:
        """检查是否有错误"""
        return (
            (self.bl0910_error_count is not None and self.bl0910_error_count > 0) or
            (self.short_period_error_count is not None and self.short_period_error_count > 0) or
            (self.long_period_error_count is not None and self.long_period_error_count > 0) or
            (self.relay_pull_fault is not None and self.relay_pull_fault > 0) or
            (self.relay_open_fault is not None and self.relay_open_fault > 0)
        )

    def to_dict(self):
        """转换为字典格式，包含解析后的数据"""
        return {
            'id': self.id,
            'device_id': self.device_id,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,

            # 原始数据
            'raw_data': {
                'bl0910_error_count': self.bl0910_error_count,
                'bl0910_rms_values': self.bl0910_rms_values,
                'relay_state': self.relay_state,
                'short_period_error_count': self.short_period_error_count,
                'long_period_error_count': self.long_period_error_count,
                'last_zero_cross_time': self.last_zero_cross_time,
                'voltage_raw': self.voltage_raw,
                'temperature_raw': self.temperature_raw,
                'total_power_raw': self.total_power_raw,
                'csq': self.csq,
                'ber': self.ber,
                'relay_pull_fault': self.relay_pull_fault,
                'relay_open_fault': self.relay_open_fault,
            },

            # 解析后的数据
            'parsed_data': {
                'voltage': self.get_voltage(),
                'temperature': self.get_temperature(),
                'total_power': self.get_total_power(),
                'relay_states': self.get_relay_states(),
                'channel_powers': self.get_all_channel_powers(),
                'has_errors': self.has_errors(),
            },

            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    @staticmethod
    def get_table_name():
        """获取当前环境的表名"""
        if os.environ.get('FLASK_ENV') == 'development':
            return 'dev_time_series_data'
        else:
            return 'prod_time_series_data'

    def __repr__(self):
        return f'<TimeSeriesData {self.device_id}@{self.timestamp}>'
